"""
套保策略相关的 Celery 任务

这些任务在独立的 Worker 进程中执行，与 Web API 服务解耦
"""
import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

from .celery_app import celery_app
from ..core.hedging_manager import HedgingStrategyManager
from ..config.queue_config import (
    HEDGING_ORDERS_QUEUE,
    SINGLE_ORDERS_QUEUE,
    MONITORING_QUEUE,
    STATUS_QUEUE
)

# 添加日志配置
try:
    from crypt_carry.utils.logger_config import get_hedging_strategy_logger
    logger = get_hedging_strategy_logger(__name__)
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

# 全局套保策略管理器实例（在 Worker 进程中）
_global_hedging_manager: Optional[HedgingStrategyManager] = None


async def get_hedging_manager() -> HedgingStrategyManager:
    """获取或创建套保策略管理器实例"""
    global _global_hedging_manager
    
    if _global_hedging_manager is None:
        logger.info("创建套保策略管理器实例...")
        _global_hedging_manager = HedgingStrategyManager()
        
        # 初始化管理器
        await _global_hedging_manager.initialize()
        logger.info("套保策略管理器初始化完成")
        
        # 启动策略监控（如果配置了自动启动）
        if _global_hedging_manager.config_manager.auto_start_monitoring:
            await _global_hedging_manager.start()
            logger.info("套保策略监控已启动")
    
    return _global_hedging_manager


def run_async_task(coro):
    """在 Celery 任务中运行异步函数的辅助函数"""
    try:
        # 尝试获取当前事件循环
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # 如果循环正在运行，创建新的任务
            return loop.run_until_complete(coro)
        else:
            # 如果循环未运行，直接运行
            return loop.run_until_complete(coro)
    except RuntimeError:
        # 如果没有事件循环，创建新的
        return asyncio.run(coro)


@celery_app.task(bind=True, queue=HEDGING_ORDERS_QUEUE)
def create_hedging_order_task(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    创建套保订单任务
    
    Args:
        order_data: 订单数据，包含所有必要的套保参数
        
    Returns:
        Dict: 执行结果
    """
    try:
        logger.info(f"开始执行套保订单任务: {order_data.get('order_id')}")
        
        async def _execute_hedging_order():
            manager = await get_hedging_manager()
            
            # 调用套保策略管理器创建订单
            result = await manager.create_hedging_order(
                order_id=order_data['order_id'],
                conditions=order_data['conditions'],
                long_exchange=order_data['long_exchange'],
                long_symbol=order_data['long_symbol'],
                short_exchange=order_data['short_exchange'],
                short_symbol=order_data['short_symbol'],
                amount=order_data['amount'],
                amount_currency=order_data['amount_currency'],
                long_is_spot=order_data.get('long_is_spot', True),
                short_is_spot=order_data.get('short_is_spot', False),
                priority=order_data.get('priority', 1)
            )
            
            return result
        
        # 执行异步任务
        result = run_async_task(_execute_hedging_order())
        
        logger.info(f"套保订单任务执行完成: {order_data.get('order_id')}, 结果: {result['success']}")
        return result
        
    except Exception as e:
        logger.error(f"套保订单任务执行失败: {order_data.get('order_id')}, 错误: {str(e)}")
        
        # Celery 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"任务将在 {self.default_retry_delay} 秒后重试...")
            raise self.retry(countdown=self.default_retry_delay, exc=e)
        
        return {
            'success': False,
            'error': f'套保订单任务执行失败: {str(e)}',
            'task_id': self.request.id,
            'retries': self.request.retries
        }


@celery_app.task(bind=True, queue=SINGLE_ORDERS_QUEUE)
def create_single_order_task(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    创建单独订单任务
    
    Args:
        order_data: 订单数据
        
    Returns:
        Dict: 执行结果
    """
    try:
        logger.info(f"开始执行单独订单任务: {order_data.get('order_id')}")
        
        async def _execute_single_order():
            # 这里可以集成实际的订单管理器
            from crypt_carry.core.exchange.account.order_manager import OrderManager
            order_manager = OrderManager.get_instance()
            
            # 执行订单
            result = await order_manager.create_order(
                exchange=order_data['exchange'],
                symbol=order_data['symbol'],
                order_type=order_data['order_type'],
                side=order_data['side'],
                amount=order_data['amount'],
                price=order_data.get('price'),
                is_spot=order_data.get('is_spot', True)
            )
            
            return {
                'success': True,
                'message': '单独订单执行成功',
                'order': result
            }
        
        # 执行异步任务
        result = run_async_task(_execute_single_order())
        
        logger.info(f"单独订单任务执行完成: {order_data.get('order_id')}")
        return result
        
    except Exception as e:
        logger.error(f"单独订单任务执行失败: {order_data.get('order_id')}, 错误: {str(e)}")
        
        # Celery 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"任务将在 {self.default_retry_delay} 秒后重试...")
            raise self.retry(countdown=self.default_retry_delay, exc=e)
        
        return {
            'success': False,
            'error': f'单独订单任务执行失败: {str(e)}',
            'task_id': self.request.id
        }


@celery_app.task(bind=True, queue=MONITORING_QUEUE)
def start_condition_monitoring_task(self, condition_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    启动条件监控任务
    
    Args:
        condition_data: 条件数据
        
    Returns:
        Dict: 执行结果
    """
    try:
        logger.info(f"开始启动条件监控: {condition_data.get('condition_id')}")
        
        async def _start_monitoring():
            manager = await get_hedging_manager()
            
            # 添加套保条件
            result = await manager.add_hedging_condition(
                condition_id=condition_data['condition_id'],
                condition_type=condition_data['condition_type'],
                long_exchange=condition_data['long_exchange'],
                long_symbol=condition_data['long_symbol'],
                short_exchange=condition_data['short_exchange'],
                short_symbol=condition_data['short_symbol'],
                trigger_value=condition_data['trigger_value'],
                comparison_operator=condition_data['comparison_operator'],
                amount=condition_data['amount'],
                amount_currency=condition_data['amount_currency'],
                long_is_spot=condition_data.get('long_is_spot', True),
                short_is_spot=condition_data.get('short_is_spot', False),
                priority=condition_data.get('priority', 1)
            )
            
            return {'success': result, 'message': '条件监控启动成功' if result else '条件监控启动失败'}
        
        # 执行异步任务
        result = run_async_task(_start_monitoring())
        
        logger.info(f"条件监控启动完成: {condition_data.get('condition_id')}")
        return result
        
    except Exception as e:
        logger.error(f"条件监控启动失败: {condition_data.get('condition_id')}, 错误: {str(e)}")
        return {
            'success': False,
            'error': f'条件监控启动失败: {str(e)}',
            'task_id': self.request.id
        }


@celery_app.task(bind=True, queue=MONITORING_QUEUE)
def stop_condition_monitoring_task(self, condition_id: str) -> Dict[str, Any]:
    """
    停止条件监控任务
    
    Args:
        condition_id: 条件ID
        
    Returns:
        Dict: 执行结果
    """
    try:
        logger.info(f"开始停止条件监控: {condition_id}")
        
        async def _stop_monitoring():
            manager = await get_hedging_manager()
            
            # 移除套保条件
            result = await manager.remove_hedging_condition(condition_id)
            
            return {'success': result, 'message': '条件监控停止成功' if result else '条件监控停止失败'}
        
        # 执行异步任务
        result = run_async_task(_stop_monitoring())
        
        logger.info(f"条件监控停止完成: {condition_id}")
        return result
        
    except Exception as e:
        logger.error(f"条件监控停止失败: {condition_id}, 错误: {str(e)}")
        return {
            'success': False,
            'error': f'条件监控停止失败: {str(e)}',
            'task_id': self.request.id
        }


@celery_app.task(bind=True, queue=STATUS_QUEUE)
def get_hedging_status_task(self) -> Dict[str, Any]:
    """
    获取套保策略状态任务
    
    Returns:
        Dict: 状态信息
    """
    try:
        async def _get_status():
            manager = await get_hedging_manager()
            
            if not manager.hedging_strategy:
                return {
                    'success': True,
                    'is_running': False,
                    'conditions': [],
                    'total_conditions': 0
                }
            
            # 获取策略状态
            conditions = []
            for condition_id, condition in manager.hedging_strategy.conditions.items():
                condition_info = {
                    'condition_id': condition_id,
                    'condition_type': condition.condition_type.value,
                    'long_exchange': condition.long_exchange,
                    'long_symbol': condition.long_symbol,
                    'short_exchange': condition.short_exchange,
                    'short_symbol': condition.short_symbol,
                    'trigger_value': condition.trigger_value,
                    'comparison_operator': condition.comparison_operator,
                    'amount': condition.amount,
                    'amount_currency': condition.amount_currency,
                    'is_active': condition.is_active,
                    'priority': condition.priority,
                    'created_at': condition.created_at.isoformat() if condition.created_at else None,
                    'status': 'monitoring' if condition.is_active else 'inactive'
                }
                conditions.append(condition_info)
            
            return {
                'success': True,
                'is_running': manager.hedging_strategy.is_running,
                'conditions': conditions,
                'total_conditions': len(conditions),
                'active_conditions': len([c for c in conditions if c['is_active']]),
                'strategy_info': {
                    'check_interval': manager.hedging_strategy.check_interval,
                    'max_positions': manager.hedging_strategy.max_positions,
                    'min_amount': manager.hedging_strategy.min_amount,
                    'max_amount': manager.hedging_strategy.max_amount
                }
            }
        
        # 执行异步任务
        result = run_async_task(_get_status())
        return result
        
    except Exception as e:
        logger.error(f"获取套保策略状态失败: {str(e)}")
        return {
            'success': False,
            'error': f'获取套保策略状态失败: {str(e)}',
            'task_id': self.request.id
        }
