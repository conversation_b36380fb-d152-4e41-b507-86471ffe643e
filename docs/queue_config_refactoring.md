# 队列配置重构文档

## 📋 概述

为了避免在多个文件中硬编码队列名称，提高代码的可维护性，我们创建了一个统一的队列配置管理系统。

## 🎯 目标

- **统一管理**: 所有队列名称在一个地方定义和管理
- **避免硬编码**: 消除分散在各个文件中的硬编码队列名称
- **易于维护**: 修改队列名称只需要在一个地方更改
- **类型安全**: 使用常量避免拼写错误
- **优先级管理**: 统一管理队列优先级配置

## 📁 新增文件

### 1. 队列配置模块

```
src/crypt_carry/strategies/hedging/config/
├── __init__.py              # 配置模块入口
└── queue_config.py          # 队列配置定义
```

### 2. 辅助脚本

```
src/scripts/hedging/
├── get_queue_names.py       # 获取队列名称的辅助脚本
└── test_queue_config_simple.py  # 队列配置测试脚本
```

## 🔧 核心配置

### 队列名称定义 (`queue_config.py`)

```python
# 队列名称常量
HEDGING_ORDERS_QUEUE = 'hedging_orders'    # 套保订单队列
SINGLE_ORDERS_QUEUE = 'single_orders'      # 单独订单队列
MONITORING_QUEUE = 'monitoring'            # 监控队列
STATUS_QUEUE = 'status'                    # 状态查询队列
DEFAULT_QUEUE = 'default'                  # 默认队列

# 队列优先级配置
QUEUE_PRIORITIES = {
    HEDGING_ORDERS_QUEUE: 10,  # 最高优先级
    SINGLE_ORDERS_QUEUE: 8,    # 高优先级
    STATUS_QUEUE: 6,           # 中等优先级
    MONITORING_QUEUE: 4,       # 较低优先级
    DEFAULT_QUEUE: 1,          # 最低优先级
}
```

### 辅助函数

```python
def get_worker_queue_list() -> str:
    """获取 Worker 启动时使用的队列列表字符串"""
    # 按优先级排序队列，返回逗号分隔的字符串
    
def get_queue_info() -> Dict:
    """获取所有队列的详细信息"""
    
def validate_queue_name(queue_name: str) -> bool:
    """验证队列名称是否有效"""
```

## 📝 修改的文件

### 1. `hedging_tasks.py`

**修改前:**
```python
@celery_app.task(bind=True, queue='hedging_orders')
def create_hedging_order_task(self, order_data):
    # ...
```

**修改后:**
```python
from ..config.queue_config import HEDGING_ORDERS_QUEUE

@celery_app.task(bind=True, queue=HEDGING_ORDERS_QUEUE)
def create_hedging_order_task(self, order_data):
    # ...
```

### 2. `celery_app.py`

**修改前:**
```python
task_routes={
    'task_name': {'queue': 'hedging_orders'},
    # ...
},
task_queues=(
    Queue('hedging_orders', routing_key='hedging_orders'),
    # ...
)
```

**修改后:**
```python
from ..config.queue_config import (
    HEDGING_ORDERS_QUEUE,
    SINGLE_ORDERS_QUEUE,
    # ...
)

task_routes={
    'task_name': {'queue': HEDGING_ORDERS_QUEUE},
    # ...
},
task_queues=(
    Queue(HEDGING_ORDERS_QUEUE, routing_key=HEDGING_ORDERS_QUEUE),
    # ...
)
```

### 3. `start_hedging_worker.sh`

**修改前:**
```bash
--queues=hedging_orders,single_orders,monitoring,status
```

**修改后:**
```bash
# 动态获取队列名称
QUEUE_NAMES=$(python3 "$SCRIPT_DIR/get_queue_names.py" 2>/dev/null || echo "hedging_orders,single_orders,monitoring,status")
--queues="$QUEUE_NAMES"
```

## 🚀 使用方法

### 1. 导入队列配置

```python
from crypt_carry.strategies.hedging.config.queue_config import (
    HEDGING_ORDERS_QUEUE,
    SINGLE_ORDERS_QUEUE,
    MONITORING_QUEUE,
    STATUS_QUEUE,
    get_worker_queue_list
)
```

### 2. 在任务装饰器中使用

```python
@celery_app.task(bind=True, queue=HEDGING_ORDERS_QUEUE)
def my_task(self, data):
    # 任务逻辑
    pass
```

### 3. 在启动脚本中使用

```bash
# 获取队列列表
QUEUE_NAMES=$(python3 get_queue_names.py)
celery worker --queues="$QUEUE_NAMES"
```

## ✅ 优势

1. **统一管理**: 所有队列名称在一个地方定义
2. **避免错误**: 减少拼写错误和不一致的队列名称
3. **易于维护**: 修改队列名称只需要在一个地方更改
4. **优先级管理**: 统一管理队列优先级，自动排序
5. **向后兼容**: 如果配置导入失败，自动回退到默认值
6. **类型安全**: 使用常量而不是字符串字面量

## 🔍 验证

### 测试队列配置

```bash
# 测试获取队列名称
python3 src/scripts/hedging/get_queue_names.py

# 测试队列配置
PYTHONPATH=src python3 -c "
from crypt_carry.strategies.hedging.config.queue_config import *
print(f'Worker队列列表: {get_worker_queue_list()}')
"
```

### 预期输出

```
hedging_orders,single_orders,status,monitoring,default
```

## 📚 最佳实践

1. **添加新队列**: 在 `queue_config.py` 中添加新的队列常量和配置
2. **修改队列名称**: 只需要在 `queue_config.py` 中修改常量值
3. **队列优先级**: 在 `QUEUE_PRIORITIES` 中配置队列优先级
4. **队列描述**: 在 `QUEUE_DESCRIPTIONS` 中添加队列描述
5. **验证配置**: 使用 `_validate_config()` 函数确保配置一致性

## 🔮 未来扩展

- 支持动态队列配置
- 添加队列监控和统计
- 支持队列负载均衡配置
- 集成队列健康检查
