#!/usr/bin/env python3
"""
简单的队列配置测试脚本

只测试队列配置模块本身，避免复杂的依赖
"""

import sys
import os

# 添加项目路径到 Python 路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(script_dir))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

def test_queue_config_only():
    """只测试队列配置模块"""
    try:
        print("🧪 测试队列配置模块...")
        
        # 直接导入队列配置模块
        sys.path.insert(0, os.path.join(src_path, 'crypt_carry', 'strategies', 'hedging', 'config'))
        import queue_config
        
        print("✅ 队列配置模块导入成功")
        
        # 测试队列名称常量
        print(f"📋 队列名称常量:")
        print(f"  - HEDGING_ORDERS_QUEUE: {queue_config.HEDGING_ORDERS_QUEUE}")
        print(f"  - SINGLE_ORDERS_QUEUE: {queue_config.SINGLE_ORDERS_QUEUE}")
        print(f"  - MONITORING_QUEUE: {queue_config.MONITORING_QUEUE}")
        print(f"  - STATUS_QUEUE: {queue_config.STATUS_QUEUE}")
        print(f"  - DEFAULT_QUEUE: {queue_config.DEFAULT_QUEUE}")
        
        # 测试队列字典
        print(f"📝 队列字典: {queue_config.QUEUE_NAMES}")
        
        # 测试辅助函数
        all_queues = queue_config.get_all_queue_names()
        print(f"📋 所有队列: {all_queues}")
        
        worker_queues = queue_config.get_worker_queue_list()
        print(f"🔧 Worker 队列列表: {worker_queues}")
        
        # 测试队列信息
        queue_info = queue_config.get_queue_info()
        print(f"📊 队列详细信息:")
        for queue_name, info in queue_info.items():
            print(f"  - {queue_name}: 优先级={info['priority']}")
        
        # 测试队列验证
        print(f"🔍 队列验证测试:")
        print(f"  - 'hedging_orders' 有效: {queue_config.validate_queue_name('hedging_orders')}")
        print(f"  - 'invalid_queue' 有效: {queue_config.validate_queue_name('invalid_queue')}")
        
        # 验证队列名称一致性
        expected_queues = {'hedging_orders', 'single_orders', 'monitoring', 'status', 'default'}
        actual_queues = set(queue_config.QUEUE_NAMES.values())
        
        if expected_queues == actual_queues:
            print("✅ 队列名称一致性检查通过")
        else:
            print(f"❌ 队列名称不一致: 期望={expected_queues}, 实际={actual_queues}")
            return False
        
        # 验证优先级配置
        for queue_name in actual_queues:
            if queue_name not in queue_config.QUEUE_PRIORITIES:
                print(f"❌ 队列 {queue_name} 缺少优先级配置")
                return False
        
        print("✅ 优先级配置检查通过")
        
        # 验证描述配置
        for queue_name in actual_queues:
            if queue_name not in queue_config.QUEUE_DESCRIPTIONS:
                print(f"❌ 队列 {queue_name} 缺少描述配置")
                return False
        
        print("✅ 描述配置检查通过")
        
        print("✅ 队列配置测试全部通过")
        return True
        
    except Exception as e:
        print(f"❌ 队列配置测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_get_queue_names_script():
    """测试获取队列名称的脚本"""
    try:
        print("\n🧪 测试获取队列名称脚本...")
        
        # 运行获取队列名称的脚本
        import subprocess
        script_path = os.path.join(script_dir, 'get_queue_names.py')
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, text=True, cwd=project_root)
        
        if result.returncode == 0:
            queue_list = result.stdout.strip()
            print(f"✅ 脚本执行成功，队列列表: {queue_list}")
            
            # 验证队列列表格式
            queues = queue_list.split(',')
            expected_queues = ['hedging_orders', 'single_orders', 'monitoring', 'status']
            
            if all(queue.strip() in expected_queues for queue in queues):
                print("✅ 队列列表格式正确")
                return True
            else:
                print(f"❌ 队列列表格式错误: {queues}")
                return False
        else:
            print(f"❌ 脚本执行失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 脚本测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 简单队列配置测试")
    print("=" * 50)
    
    success = True
    
    # 测试队列配置模块
    success &= test_queue_config_only()
    
    # 测试获取队列名称脚本
    success &= test_get_queue_names_script()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！队列配置工作正常")
        print("\n📝 总结:")
        print("  ✅ 队列配置模块正常工作")
        print("  ✅ 队列名称统一管理")
        print("  ✅ 辅助脚本正常工作")
        print("  ✅ 避免了硬编码队列名称")
    else:
        print("❌ 部分测试失败，请检查配置")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
