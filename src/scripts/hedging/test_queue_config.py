#!/usr/bin/env python3
"""
测试队列配置的脚本

验证队列配置是否正确工作
"""

import sys
import os

# 添加项目路径到 Python 路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(script_dir))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

def test_queue_config():
    """测试队列配置"""
    try:
        print("🧪 测试队列配置...")
        
        # 测试导入队列配置
        from crypt_carry.strategies.hedging.config.queue_config import (
            HEDGING_ORDERS_QUEUE,
            SINGLE_ORDERS_QUEUE,
            MONITORING_QUEUE,
            STATUS_QUEUE,
            DEFAULT_QUEUE,
            get_all_queue_names,
            get_worker_queue_list,
            get_queue_info,
            validate_queue_name
        )
        
        print("✅ 队列配置导入成功")
        
        # 测试队列名称
        print(f"📋 队列名称:")
        print(f"  - 套保订单队列: {HEDGING_ORDERS_QUEUE}")
        print(f"  - 单独订单队列: {SINGLE_ORDERS_QUEUE}")
        print(f"  - 监控队列: {MONITORING_QUEUE}")
        print(f"  - 状态队列: {STATUS_QUEUE}")
        print(f"  - 默认队列: {DEFAULT_QUEUE}")
        
        # 测试辅助函数
        all_queues = get_all_queue_names()
        print(f"📝 所有队列: {all_queues}")
        
        worker_queues = get_worker_queue_list()
        print(f"🔧 Worker 队列列表: {worker_queues}")
        
        # 测试队列信息
        queue_info = get_queue_info()
        print(f"📊 队列详细信息:")
        for queue_name, info in queue_info.items():
            print(f"  - {queue_name}: 优先级={info['priority']}, 描述={info['description']}")
        
        # 测试队列验证
        print(f"🔍 队列验证测试:")
        print(f"  - 'hedging_orders' 有效: {validate_queue_name('hedging_orders')}")
        print(f"  - 'invalid_queue' 有效: {validate_queue_name('invalid_queue')}")
        
        print("✅ 队列配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 队列配置测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_celery_config():
    """测试 Celery 配置"""
    try:
        print("\n🧪 测试 Celery 配置...")
        
        # 测试导入 Celery 应用
        from crypt_carry.strategies.hedging.tasks.celery_app import celery_app
        
        print("✅ Celery 应用导入成功")
        
        # 检查队列配置
        queues = celery_app.conf.task_queues
        print(f"📋 Celery 队列配置:")
        for queue in queues:
            print(f"  - {queue.name}: routing_key={queue.routing_key}")
        
        # 检查任务路由
        routes = celery_app.conf.task_routes
        print(f"🛣️ 任务路由配置:")
        for task_name, route_config in routes.items():
            task_short_name = task_name.split('.')[-1]
            print(f"  - {task_short_name}: queue={route_config['queue']}")
        
        print("✅ Celery 配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Celery 配置测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_task_imports():
    """测试任务导入"""
    try:
        print("\n🧪 测试任务导入...")
        
        # 测试导入任务
        from crypt_carry.strategies.hedging.tasks.hedging_tasks import (
            create_hedging_order_task,
            create_single_order_task,
            start_condition_monitoring_task,
            stop_condition_monitoring_task,
            get_hedging_status_task
        )
        
        print("✅ 任务导入成功")
        
        # 检查任务队列配置
        tasks = [
            ('create_hedging_order_task', create_hedging_order_task),
            ('create_single_order_task', create_single_order_task),
            ('start_condition_monitoring_task', start_condition_monitoring_task),
            ('stop_condition_monitoring_task', stop_condition_monitoring_task),
            ('get_hedging_status_task', get_hedging_status_task)
        ]
        
        print(f"📋 任务队列配置:")
        for task_name, task_func in tasks:
            # 获取任务的队列配置
            queue_name = getattr(task_func, 'queue', 'unknown')
            print(f"  - {task_name}: queue={queue_name}")
        
        print("✅ 任务导入测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 任务导入测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 队列配置测试")
    print("=" * 50)
    
    success = True
    
    # 测试队列配置
    success &= test_queue_config()
    
    # 测试 Celery 配置
    success &= test_celery_config()
    
    # 测试任务导入
    success &= test_task_imports()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！队列配置工作正常")
    else:
        print("❌ 部分测试失败，请检查配置")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
