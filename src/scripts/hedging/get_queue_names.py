#!/usr/bin/env python3
"""
获取队列名称的辅助脚本

用于在 shell 脚本中动态获取队列配置
"""

import sys
import os

def get_queue_names():
    """获取队列名称列表"""
    # 队列名称常量（与 queue_config.py 保持同步）
    HEDGING_ORDERS_QUEUE = 'hedging_orders'
    SINGLE_ORDERS_QUEUE = 'single_orders'
    MONITORING_QUEUE = 'monitoring'
    STATUS_QUEUE = 'status'

    # 队列优先级（与 queue_config.py 保持同步）
    QUEUE_PRIORITIES = {
        HEDGING_ORDERS_QUEUE: 10,  # 最高优先级
        SINGLE_ORDERS_QUEUE: 8,    # 高优先级
        STATUS_QUEUE: 6,           # 中等优先级
        MONITORING_QUEUE: 4,       # 较低优先级
    }

    # 按优先级排序队列
    queues = [HEDGING_ORDERS_QUEUE, SINGLE_ORDERS_QUEUE, MONITORING_QUEUE, STATUS_QUEUE]
    sorted_queues = sorted(queues, key=lambda q: QUEUE_PRIORITIES.get(q, 0), reverse=True)
    return ','.join(sorted_queues)

if __name__ == "__main__":
    try:
        # 尝试从配置文件导入（如果可能的话）
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(script_dir))
        src_path = os.path.join(project_root, 'src')
        sys.path.insert(0, src_path)

        from crypt_carry.strategies.hedging.config.queue_config import get_worker_queue_list
        print(get_worker_queue_list())

    except Exception:
        # 如果导入失败，使用本地定义的队列名称
        print(get_queue_names())
