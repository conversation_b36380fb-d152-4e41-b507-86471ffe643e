"""
消息队列配置

统一管理所有 Celery 任务队列的名称，避免硬编码分散在各个文件中
"""

from typing import List, Dict

# ============================================================================
# 队列名称常量定义
# ============================================================================

# 套保订单队列 - 处理套保订单创建任务
HEDGING_ORDERS_QUEUE = 'hedging_orders'

# 单独订单队列 - 处理单独订单创建任务  
SINGLE_ORDERS_QUEUE = 'single_orders'

# 监控队列 - 处理条件监控启动/停止任务
MONITORING_QUEUE = 'monitoring'

# 状态查询队列 - 处理状态查询任务
STATUS_QUEUE = 'status'

# 默认队列 - 处理其他未指定队列的任务
DEFAULT_QUEUE = 'default'

# ============================================================================
# 队列配置字典
# ============================================================================

QUEUE_NAMES = {
    'hedging_orders': HEDGING_ORDERS_QUEUE,
    'single_orders': SINGLE_ORDERS_QUEUE,
    'monitoring': MONITORING_QUEUE,
    'status': STATUS_QUEUE,
    'default': DEFAULT_QUEUE,
}

# ============================================================================
# 队列描述信息
# ============================================================================

QUEUE_DESCRIPTIONS = {
    HEDGING_ORDERS_QUEUE: '套保订单队列 - 处理跨交易所套保订单创建',
    SINGLE_ORDERS_QUEUE: '单独订单队列 - 处理单个交易所订单创建',
    MONITORING_QUEUE: '监控队列 - 处理条件监控的启动和停止',
    STATUS_QUEUE: '状态查询队列 - 处理策略状态查询请求',
    DEFAULT_QUEUE: '默认队列 - 处理其他未分类任务',
}

# ============================================================================
# 队列优先级配置
# ============================================================================

QUEUE_PRIORITIES = {
    HEDGING_ORDERS_QUEUE: 10,  # 最高优先级
    SINGLE_ORDERS_QUEUE: 8,    # 高优先级
    STATUS_QUEUE: 6,           # 中等优先级
    MONITORING_QUEUE: 4,       # 较低优先级
    DEFAULT_QUEUE: 1,          # 最低优先级
}

# ============================================================================
# 辅助函数
# ============================================================================

def get_all_queue_names() -> List[str]:
    """
    获取所有队列名称列表
    
    Returns:
        List[str]: 所有队列名称
    """
    return list(QUEUE_NAMES.values())


def get_worker_queue_list() -> str:
    """
    获取 Worker 启动时使用的队列列表字符串
    
    Returns:
        str: 逗号分隔的队列名称字符串，用于 celery worker --queues 参数
    """
    # 按优先级排序队列
    sorted_queues = sorted(
        QUEUE_NAMES.values(),
        key=lambda q: QUEUE_PRIORITIES.get(q, 0),
        reverse=True
    )
    return ','.join(sorted_queues)


def get_queue_info() -> Dict[str, Dict[str, any]]:
    """
    获取所有队列的详细信息
    
    Returns:
        Dict: 包含队列名称、描述、优先级的字典
    """
    return {
        queue_name: {
            'name': queue_name,
            'description': QUEUE_DESCRIPTIONS.get(queue_name, ''),
            'priority': QUEUE_PRIORITIES.get(queue_name, 1)
        }
        for queue_name in QUEUE_NAMES.values()
    }


def validate_queue_name(queue_name: str) -> bool:
    """
    验证队列名称是否有效
    
    Args:
        queue_name: 要验证的队列名称
        
    Returns:
        bool: 队列名称是否有效
    """
    return queue_name in QUEUE_NAMES.values()


# ============================================================================
# 队列配置验证
# ============================================================================

def _validate_config():
    """验证队列配置的一致性"""
    all_queues = set(QUEUE_NAMES.values())
    
    # 检查描述是否完整
    missing_descriptions = all_queues - set(QUEUE_DESCRIPTIONS.keys())
    if missing_descriptions:
        raise ValueError(f"缺少队列描述: {missing_descriptions}")
    
    # 检查优先级是否完整
    missing_priorities = all_queues - set(QUEUE_PRIORITIES.keys())
    if missing_priorities:
        raise ValueError(f"缺少队列优先级: {missing_priorities}")


# 模块加载时验证配置
_validate_config()
